const fs = require('fs');
const path = require('path');

// Simple ICO file creation for basic compatibility
// This creates a basic ICO file with PNG data embedded

function createBasicIco(pngPath, icoPath) {
  try {
    const pngData = fs.readFileSync(pngPath);
    
    // ICO file header (6 bytes)
    const header = Buffer.alloc(6);
    header.writeUInt16LE(0, 0);      // Reserved (must be 0)
    header.writeUInt16LE(1, 2);      // Image type (1 = ICO)
    header.writeUInt16LE(1, 4);      // Number of images
    
    // Image directory entry (16 bytes)
    const dirEntry = Buffer.alloc(16);
    dirEntry.writeUInt8(0, 0);       // Width (0 = 256)
    dirEntry.writeUInt8(0, 1);       // Height (0 = 256)
    dirEntry.writeUInt8(0, 2);       // Color palette (0 = no palette)
    dirEntry.writeUInt8(0, 3);       // Reserved
    dirEntry.writeUInt16LE(1, 4);    // Color planes
    dirEntry.writeUInt16LE(32, 6);   // Bits per pixel
    dirEntry.writeUInt32LE(pngData.length, 8);  // Image data size
    dirEntry.writeUInt32LE(22, 12);  // Offset to image data
    
    // Combine header, directory entry, and PNG data
    const icoData = Buffer.concat([header, dirEntry, pngData]);
    
    fs.writeFileSync(icoPath, icoData);
    console.log('✅ Created basic ICO file:', icoPath);
    return true;
  } catch (error) {
    console.error('❌ Error creating ICO file:', error.message);
    return false;
  }
}

// Create the ICO file
const pngPath = path.join(__dirname, '../src/assets/Epos.png');
const icoPath = path.join(__dirname, '../build/icon.ico');

if (fs.existsSync(pngPath)) {
  createBasicIco(pngPath, icoPath);
} else {
  console.error('❌ Source PNG file not found:', pngPath);
}

console.log('');
console.log('📋 Icon conversion completed!');
console.log('✅ Linux: build/icon.png');
console.log('✅ macOS: build/icon.icns');
console.log('✅ Windows: build/icon.ico (basic format)');
console.log('');
console.log('💡 For better Windows icon quality, consider using:');
console.log('   - Online converter: https://convertio.co/png-ico/');
console.log('   - Or professional tools like GIMP or Photoshop');
