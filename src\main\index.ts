import { app, B<PERSON>erWindow, nativeImage } from "electron";
import path from "node:path";
import os from "node:os";

import { registerRoute } from "lib/electron-router-dom";
import { setupIpcHandlers } from "./ipcHandlers";
import { envConfig } from "./config/env";
import { cleanupSystemTray } from "./handler/systemTrayHandler";

// Define the custom icon path
const getCustomIconPath = () => {
  // In development mode (even when NODE_ENV=production for dev:prod), use the source path
  // Check if we're in the actual development environment by looking at the file structure
  const devIconPath = path.join(__dirname, '../../src/assets/rounded_corners.png');
  const prodIconPath = path.join(process.resourcesPath, 'assets/rounded_corners.png');

  // Try development path first (for both dev and dev:prod scripts)
  try {
    if (require('fs').existsSync(devIconPath)) {
      return devIconPath;
    }
  } catch (error) {
    // Ignore error and try production path
  }

  // Fall back to production path
  return prodIconPath;
};

// Create a native image for the custom icon
const createCustomIcon = () => {
  try {
    const iconPath = getCustomIconPath();
    const icon = nativeImage.createFromPath(iconPath);
    if (icon.isEmpty()) {
      console.warn('⚠️ Custom icon not found, using default');
      return undefined;
    }

    // Ensure the icon is properly sized for macOS (512x512 is standard)
    const resizedIcon = icon.resize({ width: 512, height: 512 });
    console.log('✅ Custom icon loaded and resized successfully from:', iconPath);
    return resizedIcon;
  } catch (error) {
    console.error('❌ Error loading custom icon:', error);
    return undefined;
  }
};

// Global icon instance
export const customIcon = createCustomIcon();

async function createMainWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 670,
    fullscreenable: true,
    show: false,
    resizable: true,
    alwaysOnTop: false,
    focusable: true,
    icon: customIcon, // Set custom icon for the main window
    webPreferences: {
      nodeIntegration: true, // Enable Node.js integration
      contextIsolation: false, // WARNING: Disabling contextIsolation is not recommended for production builds due to security risks.
      webSecurity: true, // Keep webSecurity enabled by default when nodeIntegration is true
    },
  });

  registerRoute({
    id: "main",
    browserWindow: mainWindow,
    htmlFile: path.join(__dirname, "../renderer/index.html"),
  });

  mainWindow.on("ready-to-show", () => {
    mainWindow.show();

    // Open DevTools in development mode to see console logs
    // if (process.env.NODE_ENV === "development") {
    //   mainWindow.webContents.openDevTools();
    // }
  });
}

// Set app user data path to avoid cache permission issues
app.setPath("userData", path.join(os.tmpdir(), "electron-app-data"));

// Add command line switches to reduce cache-related errors
app.commandLine.appendSwitch("--disable-gpu-sandbox");
app.commandLine.appendSwitch("--disable-software-rasterizer");
app.commandLine.appendSwitch("--disable-gpu");

app.whenReady().then(async () => {
  setupIpcHandlers();

  // Initialize and log environment configuration
  console.log('🔧 Initializing environment configuration...');
  envConfig.logConfig();

  // Validate environment configuration
  const validation = envConfig.validateConfig();
  if (!validation.isValid) {
    console.warn('⚠️ Missing required environment variables:', validation.missingVars);
  } else {
    console.log('✅ Environment configuration is valid');
  }

  // Test database connection on startup
  try {
    console.log('🔍 Testing database connection...');
    const { testConnection } = await import('./db');
    const isConnected = await testConnection();
    if (isConnected) {
      console.log('✅ Database connection test successful');
    } else {
      console.error('❌ Database connection test failed');
    }
  } catch (error) {
    console.error('❌ Error testing database connection:', error);
  }

  // Initialize CSV folder on startup
  try {
    const { getSimpleCsvFolderPath, getSimpleCsvBackupFolderPath, ensureFolderExists } = await import('./fileSystemUtils');
    const csvFolderPath = getSimpleCsvFolderPath();
    const backupFolderPath = getSimpleCsvBackupFolderPath();
    ensureFolderExists(csvFolderPath);
    ensureFolderExists(backupFolderPath);
    console.log(`CSV monitoring folder ready at: ${csvFolderPath}`);
    console.log(`CSV backup folder ready at: ${backupFolderPath}`);

    // Auto-start CSV folder watching after a short delay to ensure all handlers are set up
    setTimeout(async () => {
      try {
        console.log('🚀 Auto-starting CSV folder monitoring from main process...');
        // Import the IPC invoke function to call the auto-start handler
        const { ipcMain } = await import('electron');

        // Simulate the auto-start-watching IPC call
        const handlers = ipcMain.listeners('auto-start-watching');
        if (handlers.length > 0) {
          // Call the handler directly
          const handler = handlers[0] as any;
          await handler();
          console.log('✅ CSV folder monitoring auto-started successfully');
        } else {
          console.log('⚠️ auto-start-watching handler not found, will start when UI loads');
        }
      } catch (error) {
        console.error('❌ Error auto-starting CSV folder monitoring:', error);
      }
    }, 2000); // Wait 2 seconds for all handlers to be registered

  } catch (error) {
    console.error('Error initializing CSV folders:', error);
  }

  createMainWindow();

  // Set dock icon for macOS
  if (process.platform === 'darwin' && customIcon && app.dock) {
    app.dock.setIcon(customIcon);
    console.log('✅ Dock icon set successfully');
  }

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) createMainWindow();
  });
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("before-quit", () => {
  console.log('🧹 Cleaning up before quit...');
  cleanupSystemTray();
});
