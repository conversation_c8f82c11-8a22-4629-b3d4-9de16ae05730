import type { Merchant, Group, Zone, Product, Category } from "../../types/merchant";

interface MerchantDetailsTabProps {
  formData: Omit<Merchant, "merchant_id" | "create_dt" | "update_dt">;
  onInputChange: (field: keyof Merchant, value: any) => void;
  editingMerchant: Merchant | null;
  groups: Group[];
  zones: Zone[];
  products: Product[];
  categories: Category[];
  mainMerchants: {merchant_id: number, merchant_name: string}[];
  readOnly?: boolean;
}

export function MerchantDetailsTab({
  formData,
  onInputChange,
  groups,
  zones,
  products,
  categories,
  mainMerchants,
  readOnly = false,
}: MerchantDetailsTabProps) {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Merchant Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.merchant_name}
              onChange={(e) => onInputChange("merchant_name", e.target.value)}
              className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                readOnly ? "bg-gray-50 cursor-not-allowed" : ""
              }`}
              placeholder="Enter merchant name"
              required
              readOnly={readOnly}
              disabled={readOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Merchant Type <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.merchant_type}
              onChange={(e) => {
                const newType = e.target.value as "main" | "sub";
                onInputChange("merchant_type", newType);
                // Clear parent merchant when switching to main type
                if (newType === "main") {
                  onInputChange("parent_merchant_id", undefined);
                }
              }}
              className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                readOnly ? "bg-gray-50 cursor-not-allowed" : ""
              }`}
              required
              disabled={readOnly}
            >
              <option value="main">Main Merchant</option>
              <option value="sub">Sub Merchant</option>
            </select>
          </div>

          {/* Parent Merchant Dropdown - Only show for sub merchants */}
          {formData.merchant_type === "sub" && (
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Parent Merchant <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.parent_merchant_id || ""}
                onChange={(e) =>
                  onInputChange("parent_merchant_id", e.target.value ? parseInt(e.target.value) : undefined)
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Parent Merchant</option>
                {mainMerchants.map((merchant) => (
                  <option key={merchant.merchant_id} value={merchant.merchant_id}>
                    {merchant.merchant_name}
                  </option>
                ))}
              </select>
              {mainMerchants.length === 0 && (
                <p className="mt-1 text-sm text-yellow-600">
                  No main merchants available. Please create a main merchant first.
                </p>
              )}
            </div>
          )}

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              VAT Number
            </label>
            <input
              type="text"
              value={formData.merchant_vat || ""}
              onChange={(e) => onInputChange("merchant_vat", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter VAT number"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Sub Merchant Name
            </label>
            <input
              type="text"
              value={formData.merchant_sub_name || ""}
              onChange={(e) =>
                onInputChange("merchant_sub_name", e.target.value)
              }
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter sub merchant name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              MCC Code
            </label>
            <input
              type="text"
              value={formData.merchant_mcc || ""}
              onChange={(e) => onInputChange("merchant_mcc", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter MCC code"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              WeChat Merchant ID
            </label>
            <input
              type="text"
              value={formData.merchant_id_wechat || ""}
              onChange={(e) =>
                onInputChange("merchant_id_wechat", e.target.value)
              }
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter WeChat merchant ID"
            />
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Phone
            </label>
            <input
              type="tel"
              value={formData.phone || ""}
              onChange={(e) => onInputChange("phone", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter phone number"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={formData.email || ""}
              onChange={(e) => onInputChange("email", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter email address"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Address
            </label>
            <textarea
              value={formData.address || ""}
              onChange={(e) => onInputChange("address", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter address"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Zip Code
            </label>
            <input
              type="text"
              value={formData.zipcode || ""}
              onChange={(e) => onInputChange("zipcode", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter zip code"
            />
          </div>
        </div>
      </div>
      {/* Contact Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Contact Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Person
            </label>
            <input
              type="text"
              value={formData.contact_person || ""}
              onChange={(e) => onInputChange("contact_person", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact person name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Email
            </label>
            <input
              type="email"
              value={formData.contact_email || ""}
              onChange={(e) => onInputChange("contact_email", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact email"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Phone
            </label>
            <input
              type="tel"
              value={formData.contact_phone || ""}
              onChange={(e) => onInputChange("contact_phone", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact phone"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Fax
            </label>
            <input
              type="tel"
              value={formData.contact_fax || ""}
              onChange={(e) => onInputChange("contact_fax", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact fax"
            />
          </div>
        </div>
      </div>

      {/* Invoice Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Invoice Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Name
            </label>
            <input
              type="text"
              value={formData.invoice_name || ""}
              onChange={(e) => onInputChange("invoice_name", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Tax ID
            </label>
            <input
              type="text"
              value={formData.invoice_tax || ""}
              onChange={(e) => onInputChange("invoice_tax", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice tax ID"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Address
            </label>
            <textarea
              value={formData.invoice_address || ""}
              onChange={(e) => onInputChange("invoice_address", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice address"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Classification Settings */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Classification Settings
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Group
            </label>
            <select
              value={formData.group_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "group_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a group</option>
              {groups.map((group) => (
                <option key={group.group_id} value={group.group_id}>
                  {group.group_name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Zone
            </label>
            <select
              value={formData.zone_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "zone_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a zone</option>
              {zones.map((zone) => (
                <option key={zone.zone_id} value={zone.zone_id}>
                  {zone.zone_name} ({zone.zone_code})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Product
            </label>
            <select
              value={formData.product_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "product_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a product</option>
              {products.map((product) => (
                <option key={product.product_id} value={product.product_id}>
                  {product.product_name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Category
            </label>
            <select
              value={formData.category_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "category_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.category_id} value={category.category_id}>
                  {category.category_name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Financial Settings */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Financial Settings
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Min Transfer Rate
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.rate_min_transfer?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "rate_min_transfer",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.00"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Transfer Fee (THB)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.transfer_fee?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "transfer_fee",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="50.00"
            />
            <p className="text-xs text-gray-500 mt-1">
              Direct fee amount used in Final Net Amount calculation
            </p>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Settlement Fee (THB)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.settlement_fee?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "settlement_fee",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="25.00"
            />
            <p className="text-xs text-gray-500 mt-1">
              Fee for settlement processing
            </p>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Withholding Tax (%)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.withholding_tax?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "withholding_tax",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="3.00"
            />
            <p className="text-xs text-gray-500 mt-1">
              Percentage rate used to calculate withholding tax on MDR amount
            </p>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Additional Information
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              value={formData.remark || ""}
              onChange={(e) => onInputChange("remark", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter any additional remarks"
              rows={3}
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="active"
              checked={formData.active}
              onChange={(e) => onInputChange("active", e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              htmlFor="active"
              className="ml-2 block text-sm text-gray-900"
            >
              Active
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
